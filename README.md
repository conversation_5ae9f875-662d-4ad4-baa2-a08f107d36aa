# Live Code Evaluator

Plugin para Visual Studio Code que permite la evaluación en tiempo real de código JavaScript/TypeScript, con funcionalidades premium similares a Quokka.js.

## Características principales
- Evaluación en tiempo real
- Visualización inline de resultados
- Soporte para imports y módulos
- Time travel debugging
- Live coverage
- Value explorer
- Exportación de resultados
- Soporte para múltiples archivos
- Logging avanzado y métricas de rendimiento

## Instalación y uso
1. Clona este repositorio o descarga el paquete .vsix
2. Instala las dependencias:
   ```bash
   npm install
   ```
3. Compila la extensión:
   ```bash
   npm run compile
   ```
4. Abre la carpeta en VS Code y presiona F5 para iniciar en modo desarrollo

## Comandos principales
- `Start Live Coding`: Inicia la evaluación en tiempo real
- `Stop Live Coding`: Detiene la evaluación
- `Clear Results`: Limpia los resultados
- `Export Results`: Exporta los resultados a archivo
- `Toggle Live Coverage`: Activa/desactiva cobertura en vivo
- `Open Time Travel Panel`: Abre el panel de time travel

## Configuración
Consulta el package.json para ver todas las opciones de configuración disponibles.

---

**Objetivo:** Ofrecer una experiencia de desarrollo superior para JavaScript/TypeScript, rivalizando con las funcionalidades premium de Quokka.js. 