import * as vscode from 'vscode';
import { EvaluationResult } from './evaluator';

const THEMES: Record<string, { color: string; errorColor: string; bgColor: string }> = {
  default: { color: '#888', errorColor: '#ff3333', bgColor: 'rgba(100,255,100,0.08)' },
  dark:    { color: '#b3e5fc', errorColor: '#ff8a80', bgColor: 'rgba(33,150,243,0.08)' },
  light:   { color: '#607d8b', errorColor: '#e57373', bgColor: 'rgba(255,235,59,0.08)' },
};

export class ResultDisplayer {
  private decorationType: vscode.TextEditorDecorationType | undefined;
  private errorDecorationType: vscode.TextEditorDecorationType | undefined;
  private coverageDecorationType: vscode.TextEditorDecorationType | undefined;
  private currentTheme: string = 'default';

  private ensureTheme(theme: string) {
    if (theme === this.currentTheme && this.decorationType && this.errorDecorationType && this.coverageDecorationType) return;
    const t = THEMES[theme] || THEMES.default;
    this.decorationType = vscode.window.createTextEditorDecorationType({
      after: { color: t.color, margin: '0 0 0 1em' },
    });
    this.errorDecorationType = vscode.window.createTextEditorDecorationType({
      after: { color: t.errorColor, margin: '0 0 0 1em' },
    });
    this.coverageDecorationType = vscode.window.createTextEditorDecorationType({
      isWholeLine: true,
      backgroundColor: t.bgColor,
    });
    this.currentTheme = theme;
  }

  displayResults(editor: vscode.TextEditor, results: EvaluationResult[], theme: string = 'default') {
    this.ensureTheme(theme);
    const decorations: vscode.DecorationOptions[] = [];
    const errorDecorations: vscode.DecorationOptions[] = [];
    for (const res of results) {
      const line = res.line;
      const range = new vscode.Range(line, Number.MAX_SAFE_INTEGER, line, Number.MAX_SAFE_INTEGER);
      if (res.error) {
        errorDecorations.push({ range, renderOptions: { after: { contentText: `// Error: ${res.error}` } } });
      } else {
        decorations.push({ range, renderOptions: { after: { contentText: `// → ${this.formatDisplayValue(res.value)}` } } });
      }
    }
    if (this.decorationType && this.errorDecorationType) {
      editor.setDecorations(this.decorationType, decorations);
      editor.setDecorations(this.errorDecorationType, errorDecorations);
    }
  }

  displayCoverage(editor: vscode.TextEditor, coveredLines: Set<number>) {
    if (!this.coverageDecorationType) return;
    const coverageDecorations: vscode.DecorationOptions[] = [];
    for (const line of coveredLines) {
      const range = new vscode.Range(line, 0, line, 0);
      coverageDecorations.push({ range });
    }
    editor.setDecorations(this.coverageDecorationType, coverageDecorations);
  }

  clearAllDecorations(editor: vscode.TextEditor) {
    if (this.decorationType) editor.setDecorations(this.decorationType, []);
    if (this.errorDecorationType) editor.setDecorations(this.errorDecorationType, []);
    if (this.coverageDecorationType) editor.setDecorations(this.coverageDecorationType, []);
  }

  private formatDisplayValue(value: any): string {
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value);
      } catch {
        return '[Object]';
      }
    }
    return String(value);
  }
} 