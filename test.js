// Archivo de prueba para Live Code Evaluator
let x = 5;
let y = 10;
let sum = x + y;
console.log('La suma es:', sum);

// Prueba con objetos
let person = {
  name: '<PERSON>',
  age: 30
};

// Prueba con arrays
let numbers = [1, 2, 3, 4, 5];
let doubled = numbers.map(n => n * 2);

// Prueba con funciones
function greet(name) {
  return `<PERSON><PERSON>, ${name}!`;
}

let greeting = greet('Mundo');

// Pruebas adicionales
let result = Math.max(1, 2, 3);
let isTrue = x > 3;
let message = `El resultado es ${result}`;
