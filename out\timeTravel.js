"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeTravelHistory = void 0;
class TimeTravelHistory {
    constructor() {
        this.history = [];
    }
    addSnapshot(line, variables) {
        this.history.push({ line, variables: { ...variables }, timestamp: Date.now() });
    }
    getHistory() {
        return this.history;
    }
    clear() {
        this.history = [];
    }
}
exports.TimeTravelHistory = TimeTravelHistory;
//# sourceMappingURL=timeTravel.js.map