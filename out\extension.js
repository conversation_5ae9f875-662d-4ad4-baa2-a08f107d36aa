"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const evaluator_1 = require("./evaluator");
const display_1 = require("./display");
const globalResultsPanel_1 = require("./globalResultsPanel");
const autoImportProvider_1 = require("./autoImportProvider");
const gitIntegration_1 = require("./gitIntegration");
const storageManager_1 = require("./storageManager");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const pythonEvaluator_1 = require("./pythonEvaluator");
const iaHelper_1 = require("./iaHelper");
const liveShareHelper_1 = require("./liveShareHelper");
let evaluator;
let displayer;
let git;
let storage;
let debounceTimer;
let isRunning = false;
let showCoverage = true;
let theme = 'default';
function activate(context) {
    vscode.window.showInformationMessage('Live Code Evaluator activado');
    evaluator = new evaluator_1.CodeEvaluator();
    displayer = new display_1.ResultDisplayer();
    git = new gitIntegration_1.GitIntegration();
    storage = new storageManager_1.StorageManager(context);
    // Registrar el proveedor de acciones de código para autoimport
    context.subscriptions.push(vscode.languages.registerCodeActionsProvider(['javascript', 'typescript'], new autoImportProvider_1.AutoImportProvider(), { providedCodeActionKinds: autoImportProvider_1.AutoImportProvider.providedCodeActionKinds }));
    // Leer configuración inicial
    const config = vscode.workspace.getConfiguration('liveCodeEvaluator');
    theme = config.get('theme', 'default');
    showCoverage = config.get('enableCoverage', true);
    // Listener para cambios de configuración
    vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration('liveCodeEvaluator.theme')) {
            theme = vscode.workspace.getConfiguration('liveCodeEvaluator').get('theme', 'default');
        }
        if (e.affectsConfiguration('liveCodeEvaluator.enableCoverage')) {
            showCoverage = vscode.workspace.getConfiguration('liveCodeEvaluator').get('enableCoverage', true);
        }
    });
    // Comando para iniciar evaluación en tiempo real
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.startLiveCoding', () => {
        startLiveEvaluation();
    }));
    // Comando para evaluar todos los archivos abiertos
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.evaluateAllOpenFiles', () => {
        evaluateAllOpenEditors();
    }));
    // Comando para detener evaluación
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.stopLiveCoding', () => {
        stopLiveEvaluation();
    }));
    // Comando para limpiar resultados
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.clearResults', () => {
        const editor = vscode.window.activeTextEditor;
        if (editor && displayer) {
            displayer.clearAllDecorations(editor);
        }
    }));
    // Comando para exportar resultados
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.exportResults', async () => {
        if (!evaluator)
            return;
        const editor = vscode.window.activeTextEditor;
        if (!editor)
            return;
        const code = editor.document.getText();
        const results = await evaluator.evaluate(code);
        const history = evaluator.timeTravel.getHistory();
        const coverage = Array.from(evaluator.coverage.getCoverage());
        const exportType = await vscode.window.showQuickPick(['JSON', 'CSV'], { placeHolder: 'Selecciona el formato de exportación' });
        if (!exportType)
            return;
        const defaultUri = vscode.Uri.file(path.join(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', `live-code-results.${exportType.toLowerCase()}`));
        const uri = await vscode.window.showSaveDialog({ defaultUri });
        if (!uri)
            return;
        let content = '';
        if (exportType === 'JSON') {
            content = JSON.stringify({ results, history, coverage }, null, 2);
        }
        else if (exportType === 'CSV') {
            content = exportToCSV(results, history, coverage);
        }
        await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));
        vscode.window.showInformationMessage('Resultados exportados a ' + uri.fsPath);
    }));
    // Comando para alternar cobertura
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.toggleCoverage', () => {
        showCoverage = !showCoverage;
        vscode.window.showInformationMessage('Live Code Evaluator: Cobertura ' + (showCoverage ? 'activada' : 'desactivada'));
        evaluateActiveEditor();
    }));
    // Comando para abrir panel de time travel
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.openTimeTravel', () => {
        openTimeTravelPanel();
    }));
    // Comando para abrir panel de resultados global
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.openGlobalPanel', () => {
        globalResultsPanel_1.GlobalResultsPanel.createOrShow(context.extensionUri);
    }));
    // Comando para insertar snippets
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.insertSnippet', async () => {
        const snippetsPath = path.join(context.extensionUri.fsPath, 'src', 'snippets.json');
        const snippets = JSON.parse(fs.readFileSync(snippetsPath, 'utf8'));
        const selected = await vscode.window.showQuickPick(snippets, { placeHolder: 'Selecciona un snippet para insertar' });
        if (selected) {
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                editor.insertSnippet(new vscode.SnippetString(selected.snippet));
            }
        }
    }));
    // Comando para evaluar Git Diff
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.evaluateGitDiff', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || !git)
            return;
        const changedLines = await git.getChangedLines(editor.document.uri);
        if (changedLines.size > 0) {
            // En lugar de evaluar solo las líneas cambiadas (lo cual es complejo),
            // resaltamos los resultados en las líneas cambiadas.
            await evaluateActiveEditor();
            if (!evaluator)
                return;
            const results = await evaluator.evaluate(editor.document.getText());
            const decorations = results
                .filter(r => changedLines.has(r.line))
                .map(r => ({
                range: new vscode.Range(r.line, 0, r.line, 0),
                renderOptions: { after: { contentText: ` // [GIT] ${JSON.stringify(r.value)}` } }
            }));
            const decorationType = vscode.window.createTextEditorDecorationType({
                after: { color: '#ffc107', margin: '0 0 0 1em' }
            });
            editor.setDecorations(decorationType, decorations);
            vscode.window.showInformationMessage(`Mostrando resultados para ${changedLines.size} líneas cambiadas.`);
        }
        else {
            vscode.window.showInformationMessage('No se encontraron cambios en este archivo.');
        }
    }));
    // Comando para restaurar sesión
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.restoreSession', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor || !storage)
            return;
        const data = storage.loadData(editor.document.uri.toString());
        if (data) {
            // Restaurar datos y volver a mostrar resultados
            // (Esto es una simplificación, la restauración completa puede ser más compleja)
            vscode.window.showInformationMessage('Sesión restaurada desde el historial.');
        }
        else {
            vscode.window.showInformationMessage('No se encontró historial para este archivo.');
        }
    }));
    // Comando para evaluar código Python
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.evaluatePython', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor)
            return;
        if (editor.document.languageId !== 'python') {
            vscode.window.showWarningMessage('El archivo activo no es Python.');
            return;
        }
        const code = editor.document.getText();
        const pyEval = new pythonEvaluator_1.PythonEvaluator();
        const result = await pyEval.evaluate(code);
        if (result.error) {
            vscode.window.showErrorMessage('Error de Python: ' + result.error);
        }
        else {
            vscode.window.showInformationMessage('Salida de Python: ' + result.output);
        }
    }));
    // Comando para pedir explicación IA de una línea
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.explainLineIA', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor)
            return;
        const line = editor.selection.active.line;
        const code = editor.document.getText();
        const ia = new iaHelper_1.IAHelper();
        vscode.window.withProgress({ location: vscode.ProgressLocation.Notification, title: 'Consultando IA...' }, async () => {
            const explanation = await ia.explainLine(code, line);
            vscode.window.showInformationMessage('IA: ' + explanation);
        });
    }));
    // Comando para iniciar colaboración con Live Share
    context.subscriptions.push(vscode.commands.registerCommand('liveCodeEvaluator.startLiveShare', async () => {
        const ls = await liveShareHelper_1.LiveShareHelper.getInstance();
        ls.startSharing();
    }));
}
function startLiveEvaluation() {
    if (isRunning)
        return;
    isRunning = true;
    vscode.window.showInformationMessage('Live Code Evaluator: Live coding iniciado');
    vscode.workspace.onDidChangeTextDocument(onDocumentChange);
    evaluateActiveEditor();
}
async function evaluateAllOpenEditors() {
    const editors = vscode.window.visibleTextEditors;
    for (const editor of editors) {
        if (!evaluator || !displayer)
            continue;
        const isTS = editor.document.languageId === 'typescript';
        evaluator.setTypeScriptMode(isTS);
        const code = editor.document.getText();
        const results = await evaluator.evaluate(code);
        displayer.displayResults(editor, results, theme);
        if (showCoverage) {
            displayer.displayCoverage(editor, evaluator.coverage.getCoverage());
        }
    }
    vscode.window.showInformationMessage('Evaluación completada en todos los archivos abiertos.');
}
function stopLiveEvaluation() {
    isRunning = false;
    vscode.window.showInformationMessage('Live Code Evaluator: Live coding detenido');
    // No se elimina el listener para permitir múltiples sesiones, pero se puede mejorar
}
function onDocumentChange(e) {
    if (!isRunning)
        return;
    if (debounceTimer)
        clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        evaluateActiveEditor();
    }, 500);
}
async function evaluateActiveEditor() {
    const editor = vscode.window.activeTextEditor;
    if (!editor || !evaluator || !displayer || !storage)
        return;
    // Detectar si es TypeScript
    const isTS = editor.document.languageId === 'typescript';
    evaluator.setTypeScriptMode(isTS);
    const code = editor.document.getText();
    const results = await evaluator.evaluate(code);
    displayer.displayResults(editor, results, theme);
    // TEMPORALMENTE DESHABILITADO: Publicar diagnósticos para autoimport
    // const diagnostics = results
    //   .filter(r => r.error && /is not defined/.test(r.error))
    //   .map(r => {
    //     const line = r.line;
    //     const docLine = editor.document.lineAt(line);
    //     const diagnostic = new vscode.Diagnostic(
    //       docLine.range,
    //       r.error || 'Unknown error',
    //       vscode.DiagnosticSeverity.Error
    //     );
    //     diagnostic.source = 'Live Code Evaluator';
    //     return diagnostic;
    //   });
    // const diagnosticCollection = vscode.languages.createDiagnosticCollection('live-code-evaluator');
    // diagnosticCollection.set(editor.document.uri, diagnostics);
    if (showCoverage) {
        displayer.displayCoverage(editor, evaluator.coverage.getCoverage());
    }
    // Notificaciones inteligentes: advertencias inline
    const smartWarnings = [];
    for (const [i, line] of code.split('\n').entries()) {
        if (/setTimeout\([^,]+,\s*0\)/.test(line)) {
            smartWarnings.push({ line: i, message: 'Uso de setTimeout(..., 0): puede causar loops o problemas de rendimiento.' });
        }
        if (/Promise(?!\.then|\.catch|\.finally)/.test(line) && !/await/.test(line)) {
            smartWarnings.push({ line: i, message: 'Promesa creada pero no esperada con await ni manejada con then/catch.' });
        }
        if (/while\s*\(true\)/.test(line)) {
            smartWarnings.push({ line: i, message: 'Bucle while(true) detectado: posible loop infinito.' });
        }
        if (/global\./.test(line)) {
            smartWarnings.push({ line: i, message: 'Uso de global: puede causar memory leaks o efectos secundarios.' });
        }
    }
    const warningDecorations = smartWarnings.map(w => ({
        range: new vscode.Range(w.line, Number.MAX_SAFE_INTEGER, w.line, Number.MAX_SAFE_INTEGER),
        renderOptions: { after: { contentText: ` // ⚠️ ${w.message}`, color: '#e6a700' } }
    }));
    const warningDecorationType = vscode.window.createTextEditorDecorationType({ after: { color: '#e6a700', margin: '0 0 0 1em' } });
    editor.setDecorations(warningDecorationType, warningDecorations);
    // Guardar datos en el almacenamiento persistente
    if (!evaluator)
        return;
    const dataToSave = {
        results,
        history: evaluator.timeTravel.getHistory(),
        coverage: Array.from(evaluator.coverage.getCoverage()),
        logs: [], // Logs temporalmente vacíos hasta implementar correctamente
        perf: evaluator.performance,
    };
    storage.saveData(editor.document.uri.toString(), dataToSave);
    if (globalResultsPanel_1.GlobalResultsPanel.currentPanel) {
        globalResultsPanel_1.GlobalResultsPanel.currentPanel.updateData(editor.document.uri.fsPath, dataToSave);
    }
    // Enviar datos a través de Live Share si está activo
    const ls = await liveShareHelper_1.LiveShareHelper.getInstance();
    if (ls) {
        const dataToShare = {
            uri: editor.document.uri.toString(),
            results,
            history: evaluator.timeTravel.getHistory(),
            coverage: Array.from(evaluator.coverage.getCoverage()),
            logs: [], // Logs temporalmente vacíos hasta implementar correctamente
            perf: evaluator.performance,
        };
        ls.sendData(dataToShare);
    }
}
function openTimeTravelPanel() {
    if (!evaluator)
        return;
    const history = evaluator.timeTravel.getHistory();
    const panel = vscode.window.createWebviewPanel('liveCodeTimeTravel', 'Live Code Time Travel', vscode.ViewColumn.Beside, { enableScripts: true });
    panel.webview.html = getTimeTravelHtml(history);
}
function getTimeTravelHtml(history) {
    // Obtener logs y métricas de rendimiento del evaluador
    const logs = evaluator?.performance && evaluator?.performance.length ? evaluator.performance : [];
    const advLogs = []; // Logs temporalmente vacíos hasta implementar correctamente
    const perf = evaluator?.performance || [];
    return `
    <html>
    <head>
      <style>
        body { font-family: sans-serif; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ccc; padding: 4px; }
        th { background: #eee; }
        .tree { font-family: monospace; }
        .tree ul { list-style: none; margin: 0 0 0 1em; padding: 0; }
        .tree li { margin: 0; padding: 0; }
        .tree .toggle { cursor: pointer; color: #007acc; }
        .log-table { margin-top: 2em; }
        .log-log { color: #222; }
        .log-error { color: #c00; }
        .log-warn { color: #e6a700; }
        .perf-table { margin-top: 2em; }
      </style>
    </head>
    <body>
      <h2>Historial de variables (Time Travel + Value Explorer)</h2>
      <table>
        <tr><th>Línea</th><th>Variables</th><th>Timestamp</th></tr>
        ${history.map((snap, idx) => `
          <tr>
            <td>${snap.line + 1}</td>
            <td><div class="tree" id="tree-${idx}"></div><script>renderTree('tree-${idx}', ${escapeHtmlForScript(JSON.stringify(snap.variables))});</script></td>
            <td>${new Date(snap.timestamp).toLocaleTimeString()}</td>
          </tr>
        `).join('')}
      </table>
      <h3>Logs avanzados</h3>
      <table class="log-table">
        <tr><th>Timestamp</th><th>Categoría</th><th>Mensaje</th></tr>
        ${(evaluator?.performance && evaluator?.performance.length ? evaluator.performance : []).map((perf, idx) => {
        const logsForLine = [];
        return logsForLine.map((log) => `
            <tr>
              <td>${new Date(log.timestamp).toLocaleTimeString()}</td>
              <td class="log-${log.category}">${log.category}</td>
              <td>${escapeHtmlForScript(log.message)}</td>
            </tr>
          `).join('');
    }).join('')}
      </table>
      <h3>Métricas de rendimiento</h3>
      <table class="perf-table">
        <tr><th>Línea</th><th>Duración (ms)</th></tr>
        ${perf.map((p) => `
          <tr><td>${p.line + 1}</td><td>${p.durationMs}</td></tr>
        `).join('')}
      </table>
      <script>
        function renderTree(containerId, data) {
          const container = document.getElementById(containerId);
          container.appendChild(createTree(data));
        }
        function createTree(obj) {
          if (obj === null) return document.createTextNode('null');
          if (typeof obj !== 'object') return document.createTextNode(JSON.stringify(obj));
          const ul = document.createElement('ul');
          for (const key in obj) {
            const li = document.createElement('li');
            if (typeof obj[key] === 'object' && obj[key] !== null) {
              const toggle = document.createElement('span');
              toggle.textContent = '[+] ' + key;
              toggle.className = 'toggle';
              let expanded = false;
              let childUl;
              toggle.onclick = () => {
                expanded = !expanded;
                toggle.textContent = (expanded ? '[-] ' : '[+] ') + key;
                if (expanded) {
                  childUl = createTree(obj[key]);
                  li.appendChild(childUl);
                } else {
                  if (childUl) li.removeChild(childUl);
                }
              };
              li.appendChild(toggle);
            } else {
              li.textContent = key + ': ' + JSON.stringify(obj[key]);
            }
            ul.appendChild(li);
          }
          return ul;
        }
      </script>
    </body>
    </html>
  `;
}
function escapeHtmlForScript(str) {
    const escapeMap = {
        '<': '\\u003C',
        '>': '\\u003E',
        '&': '\\u0026'
    };
    return str.replace(/[<>&]/g, (c) => escapeMap[c] || c);
}
function exportToCSV(results, history, coverage) {
    let csv = 'Tipo,Linea,Valor,Error,Logs,Variables,Timestamp\n';
    for (const r of results) {
        csv += `Resultado,${r.line + 1},${JSON.stringify(r.value)},${r.error || ''},${JSON.stringify(r.logs)},,,\n`;
    }
    for (const h of history) {
        csv += `Historial,${h.line + 1},,,${''},${JSON.stringify(h.variables)},${new Date(h.timestamp).toLocaleTimeString()}\n`;
    }
    for (const c of coverage) {
        csv += `Cobertura,${c + 1},,,,,,\n`;
    }
    return csv;
}
function deactivate() {
    // Aquí no es necesario guardar, ya que se guarda tras cada evaluación.
}
//# sourceMappingURL=extension.js.map