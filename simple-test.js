// Prueba simple para Live Code Evaluator
let a = 5;
let b = 10;
let sum = a + b;

// Prueba con string
let name = "<PERSON>";
let greeting = "<PERSON><PERSON> " + name;

// Prueba con array
let numbers = [1, 2, 3];
let first = numbers[0];

// Prueba con objeto
let person = { name: "<PERSON>", age: 25 };
let personName = person.name;

// Prueba con función
function multiply(x, y) {
  return x * y;
}

let result = multiply(3, 4);

// Prueba con console.log
console.log("Test completed");
