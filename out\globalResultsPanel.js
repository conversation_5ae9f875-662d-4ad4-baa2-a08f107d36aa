"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalResultsPanel = void 0;
const vscode = __importStar(require("vscode"));
class GlobalResultsPanel {
    constructor(panel, extensionUri) {
        this.disposables = [];
        this.allData = {};
        this.panel = panel;
        this.extensionUri = extensionUri;
        this.panel.onDidDispose(() => this.dispose(), null, this.disposables);
        this.update();
    }
    static createOrShow(extensionUri) {
        const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;
        if (GlobalResultsPanel.currentPanel) {
            GlobalResultsPanel.currentPanel.panel.reveal(column);
            return;
        }
        const panel = vscode.window.createWebviewPanel('globalResults', 'Panel de Resultados Global', column || vscode.ViewColumn.One, { enableScripts: true });
        GlobalResultsPanel.currentPanel = new GlobalResultsPanel(panel, extensionUri);
    }
    updateData(fileUri, data) {
        this.allData[fileUri] = data;
        this.update();
    }
    update() {
        this.panel.webview.html = this.getHtmlForWebview(this.allData);
    }
    dispose() {
        GlobalResultsPanel.currentPanel = undefined;
        this.panel.dispose();
        while (this.disposables.length) {
            const x = this.disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
    getHtmlForWebview(allData) {
        return `
      <html>
      <head>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
          body { font-family: sans-serif; }
          .file-container { margin-bottom: 1em; border: 1px solid #ccc; border-radius: 4px; }
          .file-header { background: #eee; padding: 4px; cursor: pointer; }
          .file-content { padding: 8px; display: none; }
          #search { width: 100%; padding: 4px; margin-bottom: 1em; }
          .chart-button { margin-left: 8px; cursor: pointer; color: #007acc; font-size: 0.8em; }
          #chart-container { margin-top: 1em; }
        </style>
      </head>
      <body>
        <h1>Panel de Resultados Global</h1>
        <input type="text" id="search" placeholder="Buscar en resultados..." onkeyup="filterResults()">
        <div id="results-container">
          ${Object.entries(allData).map(([uri, data]) => `
            <div class="file-container">
              <div class="file-header" onclick="toggleView(this)">${uri}</div>
              <div class="file-content">
                <h4>Resultados</h4>
                ${data.results.map((res, idx) => `
                  <div>
                    <pre>${JSON.stringify(res, null, 2)}</pre>
                    <span class="chart-button" onclick="renderChart('chart-canvas', '${JSON.stringify(res.value)}')">📊 Graficar</span>
                  </div>
                `).join('')}
                <h4>Logs</h4>
                <pre>${JSON.stringify(data.logs, null, 2)}</pre>
                <h4>Cobertura</h4>
                <pre>${data.coverage.length} líneas cubiertas</pre>
              </div>
            </div>
          `).join('')}
        </div>
        <div id="chart-container">
          <canvas id="chart-canvas"></canvas>
        </div>
        <script>
          let myChart;
          function renderChart(canvasId, dataString) {
            try {
              const data = JSON.parse(dataString);
              if (!Array.isArray(data) || !data.every(item => typeof item === 'number')) {
                alert('Solo se pueden graficar arrays de números.');
                return;
              }
              const ctx = document.getElementById(canvasId).getContext('2d');
              if (myChart) myChart.destroy();
              myChart = new Chart(ctx, {
                type: 'line',
                data: {
                  labels: data.map((_, i) => i + 1),
                  datasets: [{
                    label: 'Resultados',
                    data: data,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    fill: false
                  }]
                }
              });
            } catch (e) {
              console.error('Error al graficar:', e);
            }
          }
          function toggleView(header) {
            const content = header.nextElementSibling;
            content.style.display = content.style.display === 'block' ? 'none' : 'block';
          }
          function filterResults() {
            const searchTerm = document.getElementById('search').value.toLowerCase();
            const containers = document.querySelectorAll('.file-container');
            containers.forEach(container => {
              const text = container.textContent.toLowerCase();
              container.style.display = text.includes(searchTerm) ? '' : 'none';
            });
          }
        </script>
      </body>
      </html>
    `;
    }
}
exports.GlobalResultsPanel = GlobalResultsPanel;
//# sourceMappingURL=globalResultsPanel.js.map