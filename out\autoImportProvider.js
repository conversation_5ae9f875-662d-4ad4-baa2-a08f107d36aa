"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoImportProvider = void 0;
const vscode = __importStar(require("vscode"));
const KNOWN_IMPORTS = {
    'React': 'react',
    'useState': 'react',
    'useEffect': 'react',
    'Vue': 'vue',
    'ref': 'vue',
    'onMounted': 'vue',
    'fs': 'fs',
    'path': 'path',
    'fetch': 'node-fetch', // Example for a common polyfill
};
class AutoImportProvider {
    provideCodeActions(document, range, context, token) {
        const actions = [];
        for (const diagnostic of context.diagnostics) {
            if (diagnostic.source === 'Live Code Evaluator') {
                const match = /'([^']*)' is not defined/.exec(diagnostic.message);
                if (match && match[1]) {
                    const varName = match[1];
                    if (KNOWN_IMPORTS[varName]) {
                        actions.push(this.createImportAction(document, varName, KNOWN_IMPORTS[varName]));
                    }
                }
            }
        }
        return actions;
    }
    createImportAction(document, varName, moduleName) {
        const action = new vscode.CodeAction(`Import '${varName}' from '${moduleName}'`, vscode.CodeActionKind.QuickFix);
        action.edit = new vscode.WorkspaceEdit();
        const importStatement = `import { ${varName} } from '${moduleName}';\n`;
        action.edit.insert(document.uri, new vscode.Position(0, 0), importStatement);
        action.isPreferred = true;
        return action;
    }
}
exports.AutoImportProvider = AutoImportProvider;
AutoImportProvider.providedCodeActionKinds = [
    vscode.CodeActionKind.QuickFix
];
//# sourceMappingURL=autoImportProvider.js.map