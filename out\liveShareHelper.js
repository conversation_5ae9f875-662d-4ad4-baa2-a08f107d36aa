"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveShareHelper = void 0;
// import * as vsls from 'vsls/vscode'; // Comentado temporalmente
class LiveShareHelper {
    // private ls: vsls.LiveShare | undefined; // Comentado temporalmente
    constructor() { }
    static async getInstance() {
        if (!LiveShareHelper.instance) {
            LiveShareHelper.instance = new LiveShareHelper();
            // LiveShareHelper.instance.ls = await vsls.getApi(); // Comentado temporalmente
        }
        return LiveShareHelper.instance;
    }
    async startSharing() {
        // if (!this.ls || this.ls.session.role !== vsls.Role.Host) return; // Comentado temporalmente
        // const service = await this.ls.shareService('live-code-evaluator'); // Comentado temporalmente
        // if (service) { // Comentado temporalmente
        // service.onShare(async (data: any) => {
        // Lógica para manejar datos recibidos
        // });
        // vscode.window.showInformationMessage('Live Code Evaluator: Colaboración iniciada.');
        // }
    }
    sendData(data) {
        // if (!this.ls) return; // Comentado temporalmente
        // this.ls.notify('live-code-evaluator-data', data); // Comentado temporalmente
    }
}
exports.LiveShareHelper = LiveShareHelper;
//# sourceMappingURL=liveShareHelper.js.map