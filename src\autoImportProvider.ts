import * as vscode from 'vscode';

const KNOWN_IMPORTS: Record<string, string> = {
  'React': 'react',
  'useState': 'react',
  'useEffect': 'react',
  'Vue': 'vue',
  'ref': 'vue',
  'onMounted': 'vue',
  'fs': 'fs',
  'path': 'path',
  'fetch': 'node-fetch', // Example for a common polyfill
};

export class AutoImportProvider implements vscode.CodeActionProvider {
  public static readonly providedCodeActionKinds = [
    vscode.CodeActionKind.QuickFix
  ];

  provideCodeActions(document: vscode.TextDocument, range: vscode.Range | vscode.Selection, context: vscode.CodeActionContext, token: vscode.CancellationToken): vscode.ProviderResult<(vscode.Command | vscode.CodeAction)[]> {
    const actions: vscode.CodeAction[] = [];
    for (const diagnostic of context.diagnostics) {
      if (diagnostic.source === 'Live Code Evaluator') {
        const match = /'([^']*)' is not defined/.exec(diagnostic.message);
        if (match && match[1]) {
          const varName = match[1];
          if (KNOWN_IMPORTS[varName]) {
            actions.push(this.createImportAction(document, varName, KNOWN_IMPORTS[varName]));
          }
        }
      }
    }
    return actions;
  }

  private createImportAction(document: vscode.TextDocument, varName: string, moduleName: string): vscode.CodeAction {
    const action = new vscode.CodeAction(`Import '${varName}' from '${moduleName}'`, vscode.CodeActionKind.QuickFix);
    action.edit = new vscode.WorkspaceEdit();
    const importStatement = `import { ${varName} } from '${moduleName}';\n`;
    action.edit.insert(document.uri, new vscode.Position(0, 0), importStatement);
    action.isPreferred = true;
    return action;
  }
} 