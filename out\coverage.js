"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoverageTracker = void 0;
class CoverageTracker {
    constructor() {
        this.executedLines = new Set();
    }
    markExecuted(line) {
        this.executedLines.add(line);
    }
    getCoverage() {
        return this.executedLines;
    }
    clear() {
        this.executedLines.clear();
    }
}
exports.CoverageTracker = CoverageTracker;
//# sourceMappingURL=coverage.js.map