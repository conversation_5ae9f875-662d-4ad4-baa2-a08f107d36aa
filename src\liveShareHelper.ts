import * as vscode from 'vscode';
// import * as vsls from 'vsls/vscode'; // Comentado temporalmente

export class LiveShareHelper {
  private static instance: LiveShareHelper;
  // private ls: vsls.LiveShare | undefined; // Comentado temporalmente
  private constructor() {}

  public static async getInstance(): Promise<LiveShareHelper> {
    if (!LiveShareHelper.instance) {
      LiveShareHelper.instance = new LiveShareHelper();
      // LiveShareHelper.instance.ls = await vsls.getApi(); // Comentado temporalmente
    }
    return LiveShareHelper.instance;
  }

  public async startSharing() {
    // if (!this.ls || this.ls.session.role !== vsls.Role.Host) return; // Comentado temporalmente
    // const service = await this.ls.shareService('live-code-evaluator'); // Comentado temporalmente
    // if (service) { // Comentado temporalmente
      // service.onShare(async (data: any) => {
        // Lógica para manejar datos recibidos
      // });
      // vscode.window.showInformationMessage('Live Code Evaluator: Colaboración iniciada.');
    // }
  }

  public sendData(data: any) {
    // if (!this.ls) return; // Comentado temporalmente
    // this.ls.notify('live-code-evaluator-data', data); // Comentado temporalmente
  }
}