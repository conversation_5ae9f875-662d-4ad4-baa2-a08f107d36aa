import * as vscode from 'vscode';
import { webSearch } from './webSearch'; // Simulación de integración IA

export class IAHelper {
  async explainLine(code: string, line: number): Promise<string> {
    // Simulación: en un caso real, aquí se llamaría a una API de IA (OpenAI, Copilot, etc.)
    // Aquí usamos una búsqueda web simulada para la demo
    const lineContent = code.split('\n')[line] || '';
    const query = `Explica el siguiente código JavaScript: ${lineContent}`;
    const result = await webSearch(query);
    return result;
  }
} 