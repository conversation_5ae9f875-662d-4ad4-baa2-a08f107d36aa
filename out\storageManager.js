"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageManager = void 0;
class StorageManager {
    constructor(context) {
        this.context = context;
    }
    saveData(fileUri, data) {
        const key = this.getStorageKey(fileUri);
        this.context.workspaceState.update(key, data);
    }
    loadData(fileUri) {
        const key = this.getStorageKey(fileUri);
        return this.context.workspaceState.get(key);
    }
    clearData(fileUri) {
        const key = this.getStorageKey(fileUri);
        this.context.workspaceState.update(key, undefined);
    }
    getStorageKey(fileUri) {
        return `${StorageManager.STORAGE_KEY_PREFIX}${fileUri}`;
    }
}
exports.StorageManager = StorageManager;
StorageManager.STORAGE_KEY_PREFIX = 'live-code-evaluator-history-';
//# sourceMappingURL=storageManager.js.map