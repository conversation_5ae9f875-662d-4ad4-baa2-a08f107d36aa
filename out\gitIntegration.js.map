{"version": 3, "file": "gitIntegration.js", "sourceRoot": "", "sources": ["../src/gitIntegration.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,iDAAqC;AACrC,2CAA6B;AAE7B,MAAa,cAAc;IAClB,KAAK,CAAC,eAAe,CAAC,IAAgB;QAC3C,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;YAChE,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;QACD,MAAM,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAA,oBAAI,EAAC,iCAAiC,QAAQ,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;gBAClF,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4DAA4D,CAAC,CAAC;oBAC7F,OAAO,CAAC,KAAK,CAAC,sBAAsB,QAAQ,KAAK,MAAM,EAAE,CAAC,CAAC;oBAC3D,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;oBACnB,OAAO;gBACT,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,SAAS,GAAG,uCAAuC,CAAC;QAC1D,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtB,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3C,CAAC;gBACD,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;YAC1B,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3D,WAAW,EAAE,CAAC;gBACd,YAAY,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACpC,CAAC;iBAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjC,WAAW,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AA5CD,wCA4CC"}