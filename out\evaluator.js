"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeEvaluator = void 0;
const vm = __importStar(require("vm"));
const coverage_1 = require("./coverage");
const timeTravel_1 = require("./timeTravel");
const ts = __importStar(require("typescript"));
class CodeEvaluator {
    constructor() {
        this.logs = [];
        this.performance = [];
        this.isTypeScript = false;
        this.coverage = new coverage_1.CoverageTracker();
        this.timeTravel = new timeTravel_1.TimeTravelHistory();
    }
    setTypeScriptMode(isTS) {
        this.isTypeScript = isTS;
    }
    setupContext() {
        const context = vm.createContext({
            console: {
                log: (...args) => this.logs.push({ timestamp: Date.now(), category: 'log', message: args.join(' ') }),
                error: (...args) => this.logs.push({ timestamp: Date.now(), category: 'error', message: args.join(' ') }),
                warn: (...args) => this.logs.push({ timestamp: Date.now(), category: 'warn', message: args.join(' ') }),
            },
            require: this.createSafeRequire(),
            exports: {},
            module: {},
            process: {},
            setTimeout,
            setInterval,
            clearTimeout,
            clearInterval,
        });
        return context;
    }
    createSafeRequire() {
        // Solo permite importar módulos explícitamente permitidos
        const allowed = ['react', 'react-dom', 'vue', '@vue/runtime-dom', 'fs', 'path'];
        const safeRequire = (mod) => {
            if (allowed.includes(mod)) {
                try {
                    return require(mod);
                }
                catch (error) {
                    // Si el módulo opcional no está disponible, devolver un mock básico
                    if (mod === 'react') {
                        return { createElement: () => ({}), useState: () => [null, () => { }], useEffect: () => { } };
                    }
                    if (mod === 'vue') {
                        return { ref: () => ({}), onMounted: () => { } };
                    }
                    throw new Error(`Módulo opcional '${mod}' no está instalado`);
                }
            }
            throw new Error('Importación no permitida: ' + mod);
        };
        return safeRequire;
    }
    extractVariables(context) {
        const skip = [
            'console', 'require', 'exports', 'module', 'process',
            'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'
        ];
        const vars = {};
        for (const key of Object.keys(context)) {
            if (!skip.includes(key)) {
                vars[key] = context[key];
            }
        }
        return vars;
    }
    transpileIfTS(code) {
        if (!this.isTypeScript)
            return code;
        return ts.transpile(code, { module: ts.ModuleKind.CommonJS, target: ts.ScriptTarget.ES2020 });
    }
    async evaluate(code) {
        this.logs = [];
        this.coverage.clear();
        this.timeTravel.clear();
        this.performance = [];
        const results = [];
        const context = this.setupContext();
        const lines = code.split('\n');
        let accumulated = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.trim() === '')
                continue;
            accumulated += line + '\n';
            const start = Date.now();
            try {
                let jsCode = this.transpileIfTS(accumulated);
                // Soporte para async/await: si detecta 'await', evalúa como async function
                let result;
                if (/\bawait\b/.test(jsCode)) {
                    jsCode = `(async () => {${jsCode}})()`;
                    const script = new vm.Script(jsCode);
                    result = await script.runInContext(context, { timeout: 5000 });
                }
                else {
                    const script = new vm.Script(jsCode);
                    result = script.runInContext(context, { timeout: 5000 });
                }
                const duration = Date.now() - start;
                results.push({ line: i, value: result, logs: [...this.logs], perf: { line: i, durationMs: duration } });
                this.coverage.markExecuted(i);
                this.timeTravel.addSnapshot(i, this.extractVariables(context));
                this.performance.push({ line: i, durationMs: duration });
            }
            catch (err) {
                const duration = Date.now() - start;
                results.push({ line: i, value: undefined, error: err.message, logs: [...this.logs], perf: { line: i, durationMs: duration } });
                this.performance.push({ line: i, durationMs: duration });
            }
            this.logs = [];
        }
        return results;
    }
}
exports.CodeEvaluator = CodeEvaluator;
//# sourceMappingURL=evaluator.js.map