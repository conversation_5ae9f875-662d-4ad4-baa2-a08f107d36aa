"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeEvaluator = void 0;
const vm = __importStar(require("vm"));
const coverage_1 = require("./coverage");
const timeTravel_1 = require("./timeTravel");
const ts = __importStar(require("typescript"));
class CodeEvaluator {
    constructor() {
        this.logs = [];
        this.performance = [];
        this.isTypeScript = false;
        this.coverage = new coverage_1.CoverageTracker();
        this.timeTravel = new timeTravel_1.TimeTravelHistory();
    }
    setTypeScriptMode(isTS) {
        this.isTypeScript = isTS;
    }
    setupContext() {
        const context = vm.createContext({
            console: {
                log: (...args) => this.logs.push({ timestamp: Date.now(), category: 'log', message: args.join(' ') }),
                error: (...args) => this.logs.push({ timestamp: Date.now(), category: 'error', message: args.join(' ') }),
                warn: (...args) => this.logs.push({ timestamp: Date.now(), category: 'warn', message: args.join(' ') }),
            },
            require: this.createSafeRequire(),
            exports: {},
            module: {},
            process: {},
            setTimeout,
            setInterval,
            clearTimeout,
            clearInterval,
        });
        return context;
    }
    createSafeRequire() {
        // Solo permite importar módulos explícitamente permitidos
        const allowed = ['react', 'react-dom', 'vue', '@vue/runtime-dom', 'fs', 'path'];
        const safeRequire = (mod) => {
            if (allowed.includes(mod)) {
                try {
                    return require(mod);
                }
                catch (error) {
                    // Si el módulo opcional no está disponible, devolver un mock básico
                    if (mod === 'react') {
                        return { createElement: () => ({}), useState: () => [null, () => { }], useEffect: () => { } };
                    }
                    if (mod === 'vue') {
                        return { ref: () => ({}), onMounted: () => { } };
                    }
                    throw new Error(`Módulo opcional '${mod}' no está instalado`);
                }
            }
            throw new Error('Importación no permitida: ' + mod);
        };
        return safeRequire;
    }
    extractVariables(context) {
        // Para obtener variables declaradas con let/const/var, necesitamos evaluarlas como scripts
        const vars = {};
        // Intentar obtener variables conocidas del contexto
        const skip = [
            'console', 'require', 'exports', 'module', 'process',
            'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'
        ];
        // Primero obtener propiedades directas del contexto
        for (const key of Object.keys(context)) {
            if (!skip.includes(key)) {
                vars[key] = context[key];
            }
        }
        return vars;
    }
    transpileIfTS(code) {
        if (!this.isTypeScript)
            return code;
        return ts.transpile(code, { module: ts.ModuleKind.CommonJS, target: ts.ScriptTarget.ES2020 });
    }
    async evaluate(code) {
        this.logs = [];
        this.coverage.clear();
        this.timeTravel.clear();
        this.performance = [];
        const results = [];
        const context = this.setupContext();
        const lines = code.split('\n');
        // ESTRATEGIA FINAL: Evaluar línea por línea SIN acumular, usando contexto persistente
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            // Saltar líneas vacías y comentarios
            if (line === '' || line.startsWith('//')) {
                continue;
            }
            const start = Date.now();
            try {
                let result = await this.evaluateLineInContext(line, context);
                const duration = Date.now() - start;
                results.push({
                    line: i,
                    value: result,
                    logs: [...this.logs],
                    perf: { line: i, durationMs: duration }
                });
                this.coverage.markExecuted(i);
                this.timeTravel.addSnapshot(i, this.extractVariables(context));
                this.performance.push({ line: i, durationMs: duration });
            }
            catch (err) {
                const duration = Date.now() - start;
                // Solo mostrar errores reales, no errores de sintaxis esperados
                if (!this.isExpectedSyntaxError(err.message)) {
                    results.push({
                        line: i,
                        value: undefined,
                        error: err.message,
                        logs: [...this.logs],
                        perf: { line: i, durationMs: duration }
                    });
                }
                this.performance.push({ line: i, durationMs: duration });
            }
            this.logs = [];
        }
        return results;
    }
    async evaluateLineInContext(line, context) {
        // Evaluar una línea individual en el contexto persistente
        try {
            let jsCode = this.transpileIfTS(line);
            // Si es una declaración, ejecutarla y devolver el valor de la variable
            if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
                const script = new vm.Script(jsCode);
                script.runInContext(context, { timeout: 1000 });
                // Extraer el nombre de la variable y devolver su valor
                const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
                if (varMatch) {
                    const varName = varMatch[1];
                    try {
                        // Evaluar la variable como un script separado para obtener su valor
                        const getVarScript = new vm.Script(varName);
                        return getVarScript.runInContext(context, { timeout: 1000 });
                    }
                    catch {
                        return undefined;
                    }
                }
                return undefined;
            }
            // Si es una asignación, ejecutarla y devolver el valor
            if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
                const script = new vm.Script(jsCode);
                const result = script.runInContext(context, { timeout: 1000 });
                return result;
            }
            // Si es una función, ejecutarla (no devolver nada)
            if (line.includes('function ')) {
                const script = new vm.Script(jsCode);
                script.runInContext(context, { timeout: 1000 });
                return undefined;
            }
            // Si es console.log, ejecutarlo
            if (line.includes('console.log')) {
                const script = new vm.Script(jsCode);
                return script.runInContext(context, { timeout: 1000 });
            }
            // Para cualquier otra expresión, evaluarla
            if (/\bawait\b/.test(jsCode)) {
                jsCode = `(async () => { return ${jsCode.replace(/;$/, '')}; })()`;
                const script = new vm.Script(jsCode);
                return await script.runInContext(context, { timeout: 1000 });
            }
            else {
                // Si termina en ;, quitarlo para evaluar como expresión
                const cleanLine = jsCode.endsWith(';') ? jsCode.slice(0, -1) : jsCode;
                const script = new vm.Script(cleanLine);
                return script.runInContext(context, { timeout: 1000 });
            }
        }
        catch (err) {
            throw err;
        }
    }
    extractLineValue(line, context) {
        try {
            // Si es una declaración de variable, obtener su valor del contexto
            if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
                const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
                if (varMatch) {
                    const varName = varMatch[1];
                    return context[varName];
                }
            }
            // Si es una asignación, obtener el valor asignado
            if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
                const assignMatch = /(\w+)\s*=/.exec(line);
                if (assignMatch) {
                    const varName = assignMatch[1];
                    return context[varName];
                }
            }
            // Si es una llamada a función, intentar evaluarla
            if (line.includes('(') && line.includes(')') && !line.includes('function')) {
                try {
                    const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
                    const script = new vm.Script(cleanLine);
                    return script.runInContext(context, { timeout: 1000 });
                }
                catch {
                    return undefined;
                }
            }
            // Si es una expresión simple, evaluarla
            if (!line.includes('function') && !line.includes('{') && !line.includes('}') &&
                !line.includes('console.log')) {
                try {
                    const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
                    const script = new vm.Script(cleanLine);
                    return script.runInContext(context, { timeout: 1000 });
                }
                catch {
                    return undefined;
                }
            }
            return undefined;
        }
        catch {
            return undefined;
        }
    }
    fallbackEvaluation(code, lines, context) {
        const results = [];
        // Evaluar línea por línea de forma segura
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line === '' || line.startsWith('//')) {
                continue;
            }
            const start = Date.now();
            try {
                // Intentar evaluar solo esta línea
                let result;
                if (line.includes('console.log')) {
                    const script = new vm.Script(line);
                    result = script.runInContext(context, { timeout: 1000 });
                }
                else if (!line.includes('let ') && !line.includes('const ') && !line.includes('var ') &&
                    !line.includes('function') && !line.includes('{') && !line.includes('}')) {
                    const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
                    const script = new vm.Script(cleanLine);
                    result = script.runInContext(context, { timeout: 1000 });
                }
                const duration = Date.now() - start;
                results.push({
                    line: i,
                    value: result,
                    logs: [...this.logs],
                    perf: { line: i, durationMs: duration }
                });
            }
            catch (err) {
                const duration = Date.now() - start;
                if (!this.isExpectedSyntaxError(err.message)) {
                    results.push({
                        line: i,
                        value: undefined,
                        error: err.message,
                        logs: [...this.logs],
                        perf: { line: i, durationMs: duration }
                    });
                }
            }
            this.logs = [];
        }
        return results;
    }
    getLineResult(line, context, fullResult) {
        // Intentar extraer el resultado específico de esta línea
        try {
            // Si es una declaración de variable, obtener su valor
            if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
                const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
                if (varMatch) {
                    return context[varMatch[1]];
                }
            }
            // Si es una asignación, obtener el valor asignado
            if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
                const assignMatch = /(\w+)\s*=/.exec(line);
                if (assignMatch) {
                    return context[assignMatch[1]];
                }
            }
            // Si es una expresión simple, evaluarla
            if (!line.includes('function') && !line.includes('{') && !line.includes('}')) {
                try {
                    const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
                    const script = new vm.Script(cleanLine);
                    return script.runInContext(context, { timeout: 1000 });
                }
                catch {
                    return fullResult;
                }
            }
            return fullResult;
        }
        catch {
            return fullResult;
        }
    }
    evaluateSingleLine(line, context) {
        // Evaluar una línea individual en el contexto actual
        try {
            if (line.includes('console.log')) {
                const script = new vm.Script(line);
                return script.runInContext(context, { timeout: 1000 });
            }
            // Si es una expresión, evaluarla
            if (!line.includes('let ') && !line.includes('const ') && !line.includes('var ') &&
                !line.includes('function') && !line.includes('{') && !line.includes('}')) {
                const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
                const script = new vm.Script(cleanLine);
                return script.runInContext(context, { timeout: 1000 });
            }
            return undefined;
        }
        catch {
            return undefined;
        }
    }
    isExpectedSyntaxError(message) {
        const expectedErrors = [
            'Unexpected end of input',
            'Unexpected token',
            'Missing } in compound statement',
            'Unexpected identifier',
            'already been declared'
        ];
        return expectedErrors.some(error => message.includes(error));
    }
}
exports.CodeEvaluator = CodeEvaluator;
//# sourceMappingURL=evaluator.js.map