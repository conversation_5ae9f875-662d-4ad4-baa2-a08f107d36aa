import { spawn } from 'child_process';

export class PythonEvaluator {
  async evaluate(code: string): Promise<{ output: string; error?: string }> {
    return new Promise((resolve) => {
      const py = spawn('python', ['-c', code]);
      let output = '';
      let error = '';
      py.stdout.on('data', (data) => {
        output += data.toString();
      });
      py.stderr.on('data', (data) => {
        error += data.toString();
      });
      py.on('close', () => {
        resolve({ output, error: error || undefined });
      });
    });
  }
} 