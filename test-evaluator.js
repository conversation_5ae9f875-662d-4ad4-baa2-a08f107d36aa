// Test simple del evaluador
const vm = require('vm');

function testEvaluator() {
  console.log('=== TESTING EVALUATOR ===');

  const contextObj = {
    console: {
      log: (...args) => console.log('VM LOG:', ...args)
    }
  };
  const context = vm.createContext(contextObj);
  
  const lines = [
    'let a = 5;',
    'let b = 10;', 
    'let sum = a + b;',
    'console.log("Sum is:", sum);'
  ];
  
  console.log('\n--- Testing line by line evaluation ---');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    console.log(`\nEvaluating line ${i + 1}: ${line}`);
    
    try {
      const script = new vm.Script(line);
      const result = script.runInContext(context, { timeout: 1000 });
      console.log(`Result: ${result}`);
      
      // Si es una declaración, obtener el valor de la variable
      if (line.startsWith('let ')) {
        const varMatch = /let\s+(\w+)/.exec(line);
        if (varMatch) {
          const varName = varMatch[1];
          // Intentar obtener la variable del contexto global
          try {
            const getVarScript = new vm.Script(varName);
            const varValue = getVarScript.runInContext(context);
            console.log(`Variable ${varName} = ${varValue}`);
          } catch (e) {
            console.log(`Could not get variable ${varName}: ${e.message}`);
          }
        }
      }
      
    } catch (error) {
      console.log(`ERROR: ${error.message}`);
    }
  }
  
  console.log('\n--- Final context ---');
  try {
    const aScript = new vm.Script('a');
    console.log('a =', aScript.runInContext(context));
  } catch (e) { console.log('a = undefined'); }

  try {
    const bScript = new vm.Script('b');
    console.log('b =', bScript.runInContext(context));
  } catch (e) { console.log('b = undefined'); }

  try {
    const sumScript = new vm.Script('sum');
    console.log('sum =', sumScript.runInContext(context));
  } catch (e) { console.log('sum = undefined'); }
}

testEvaluator();
