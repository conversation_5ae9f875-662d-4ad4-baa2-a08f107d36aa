import * as vscode from 'vscode';

export interface PersistentData {
  results: any[];
  history: any[];
  coverage: number[];
  logs: any[];
  perf: any[];
}

export class StorageManager {
  private static readonly STORAGE_KEY_PREFIX = 'live-code-evaluator-history-';

  constructor(private context: vscode.ExtensionContext) {}

  public saveData(fileUri: string, data: PersistentData): void {
    const key = this.getStorageKey(fileUri);
    this.context.workspaceState.update(key, data);
  }

  public loadData(fileUri: string): PersistentData | undefined {
    const key = this.getStorageKey(fileUri);
    return this.context.workspaceState.get(key);
  }

  public clearData(fileUri: string): void {
    const key = this.getStorageKey(fileUri);
    this.context.workspaceState.update(key, undefined);
  }

  private getStorageKey(fileUri: string): string {
    return `${StorageManager.STORAGE_KEY_PREFIX}${fileUri}`;
  }
} 