"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitIntegration = void 0;
const vscode = __importStar(require("vscode"));
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
class GitIntegration {
    async getChangedLines(file) {
        const workspaceFolder = vscode.workspace.getWorkspaceFolder(file);
        if (!workspaceFolder) {
            vscode.window.showWarningMessage('File is not in a workspace.');
            return new Set();
        }
        const cwd = workspaceFolder.uri.fsPath;
        const filePath = path.relative(cwd, file.fsPath);
        return new Promise((resolve) => {
            (0, child_process_1.exec)(`git diff --unified=0 HEAD -- "${filePath}"`, { cwd }, (err, stdout, stderr) => {
                if (err) {
                    vscode.window.showErrorMessage('Failed to run git diff. Is Git installed and in your PATH?');
                    console.error(`Git diff error for ${filePath}: ${stderr}`);
                    resolve(new Set());
                    return;
                }
                resolve(this.parseDiff(stdout));
            });
        });
    }
    parseDiff(diff) {
        const changedLines = new Set();
        const lines = diff.split('\n');
        const hunkRegex = /@@ -\d+(?:,\d+)? \+(\d+)(?:,\d+)? @@/g;
        let currentLine = 0;
        for (const line of lines) {
            if (line.startsWith('@@')) {
                const match = hunkRegex.exec(line);
                if (match && match[1]) {
                    currentLine = parseInt(match[1], 10) - 1;
                }
                hunkRegex.lastIndex = 0;
            }
            else if (line.startsWith('+') && !line.startsWith('+++')) {
                currentLine++;
                changedLines.add(currentLine - 1);
            }
            else if (!line.startsWith('-')) {
                currentLine++;
            }
        }
        return changedLines;
    }
}
exports.GitIntegration = GitIntegration;
//# sourceMappingURL=gitIntegration.js.map