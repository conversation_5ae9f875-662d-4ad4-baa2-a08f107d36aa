"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PythonEvaluator = void 0;
const child_process_1 = require("child_process");
class PythonEvaluator {
    async evaluate(code) {
        return new Promise((resolve) => {
            const py = (0, child_process_1.spawn)('python', ['-c', code]);
            let output = '';
            let error = '';
            py.stdout.on('data', (data) => {
                output += data.toString();
            });
            py.stderr.on('data', (data) => {
                error += data.toString();
            });
            py.on('close', () => {
                resolve({ output, error: error || undefined });
            });
        });
    }
}
exports.PythonEvaluator = PythonEvaluator;
//# sourceMappingURL=pythonEvaluator.js.map