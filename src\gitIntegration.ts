import * as vscode from 'vscode';
import { exec } from 'child_process';
import * as path from 'path';

export class GitIntegration {
  public async getChangedLines(file: vscode.Uri): Promise<Set<number>> {
    const workspaceFolder = vscode.workspace.getWorkspaceFolder(file);
    if (!workspaceFolder) {
      vscode.window.showWarningMessage('File is not in a workspace.');
      return new Set();
    }
    const cwd = workspaceFolder.uri.fsPath;
    const filePath = path.relative(cwd, file.fsPath);

    return new Promise((resolve) => {
      exec(`git diff --unified=0 HEAD -- "${filePath}"`, { cwd }, (err, stdout, stderr) => {
        if (err) {
          vscode.window.showErrorMessage('Failed to run git diff. Is Git installed and in your PATH?');
          console.error(`Git diff error for ${filePath}: ${stderr}`);
          resolve(new Set());
          return;
        }
        resolve(this.parseDiff(stdout));
      });
    });
  }

  private parseDiff(diff: string): Set<number> {
    const changedLines = new Set<number>();
    const lines = diff.split('\n');
    const hunkRegex = /@@ -\d+(?:,\d+)? \+(\d+)(?:,\d+)? @@/g;
    let currentLine = 0;
    for (const line of lines) {
      if (line.startsWith('@@')) {
        const match = hunkRegex.exec(line);
        if (match && match[1]) {
          currentLine = parseInt(match[1], 10) - 1;
        }
        hunkRegex.lastIndex = 0;
      } else if (line.startsWith('+') && !line.startsWith('+++')) {
        currentLine++;
        changedLines.add(currentLine - 1);
      } else if (!line.startsWith('-')) {
        currentLine++;
      }
    }
    return changedLines;
  }
} 