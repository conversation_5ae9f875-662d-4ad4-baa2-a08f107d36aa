"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IAHelper = void 0;
const webSearch_1 = require("./webSearch"); // Simulación de integración IA
class IAHelper {
    async explainLine(code, line) {
        // Simulación: en un caso real, aquí se llamaría a una API de IA (OpenAI, Copilot, etc.)
        // Aquí usamos una búsqueda web simulada para la demo
        const lineContent = code.split('\n')[line] || '';
        const query = `Explica el siguiente código JavaScript: ${lineContent}`;
        const result = await (0, webSearch_1.webSearch)(query);
        return result;
    }
}
exports.IAHelper = IAHelper;
//# sourceMappingURL=iaHelper.js.map