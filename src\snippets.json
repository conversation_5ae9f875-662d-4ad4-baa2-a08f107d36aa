[{"label": "Función Asíncrona (Async/Await)", "description": "Inserta una función asíncrona de ejemplo", "snippet": "async function fetchData() {\n  const response = await fetch('https://api.example.com/data');\n  const data = await response.json();\n  return data;\n}"}, {"label": "Hook de React (useEffect)", "description": "Inserta un hook useEffect de React", "snippet": "import { useEffect, useState } from 'react';\n\nfunction MyComponent() {\n  const [data, setData] = useState(null);\n  useEffect(() => {\n    fetch('https://api.example.com/data')\n      .then(res => res.json())\n      .then(setData);\n  }, []);\n  return <div>{JSON.stringify(data)}</div>;\n}"}, {"label": "Componente de Vue (Composition API)", "description": "Inserta un componente de Vue 3 con Composition API", "snippet": "import { ref, onMounted } from 'vue';\n\nexport default {\n  setup() {\n    const data = ref(null);\n    onMounted(async () => {\n      const res = await fetch('https://api.example.com/data');\n      data.value = await res.json();\n    });\n    return { data };\n  }\n}"}, {"label": "Utilidad de Node.js (Leer archivo)", "description": "Inserta una función para leer un archivo con Node.js", "snippet": "import * as fs from 'fs';\n\nfunction readFile(path) {\n  return fs.readFileSync(path, 'utf8');\n}"}]