export interface VariableSnapshot {
  line: number;
  variables: Record<string, any>;
  timestamp: number;
}

export class TimeTravelHistory {
  private history: VariableSnapshot[] = [];

  addSnapshot(line: number, variables: Record<string, any>) {
    this.history.push({ line, variables: { ...variables }, timestamp: Date.now() });
  }

  getHistory(): VariableSnapshot[] {
    return this.history;
  }

  clear() {
    this.history = [];
  }
} 