import * as vm from 'vm';
import { CoverageTracker } from './coverage';
import { TimeTravelHistory } from './timeTravel';
import * as ts from 'typescript';

export interface EvaluationResult {
  line: number;
  value: any;
  error?: string;
  logs: AdvancedLogEntry[];
  perf?: PerformanceMetric;
}

export interface AdvancedLogEntry {
  timestamp: number;
  category: 'log' | 'error' | 'warn';
  message: string;
}

export interface PerformanceMetric {
  line: number;
  durationMs: number;
}

export class CodeEvaluator {
  private logs: AdvancedLogEntry[] = [];
  public coverage: CoverageTracker;
  public timeTravel: TimeTravelHistory;
  public performance: PerformanceMetric[] = [];
  private isTypeScript: boolean = false;

  constructor() {
    this.coverage = new CoverageTracker();
    this.timeTravel = new TimeTravelHistory();
  }

  setTypeScriptMode(isTS: boolean) {
    this.isTypeScript = isTS;
  }

  private setupContext(): vm.Context {
    const context = vm.createContext({
      console: {
        log: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'log', message: args.join(' ') }),
        error: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'error', message: args.join(' ') }),
        warn: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'warn', message: args.join(' ') }),
      },
      require: this.createSafeRequire(),
      exports: {},
      module: {},
      process: {},
      setTimeout,
      setInterval,
      clearTimeout,
      clearInterval,
    });
    return context;
  }

  private createSafeRequire() {
    // Solo permite importar módulos explícitamente permitidos
    const allowed = ['react', 'react-dom', 'vue', '@vue/runtime-dom', 'fs', 'path'];
    const safeRequire = (mod: string) => {
      if (allowed.includes(mod)) {
        try {
          return require(mod);
        } catch (error) {
          // Si el módulo opcional no está disponible, devolver un mock básico
          if (mod === 'react') {
            return { createElement: () => ({}), useState: () => [null, () => {}], useEffect: () => {} };
          }
          if (mod === 'vue') {
            return { ref: () => ({}), onMounted: () => {} };
          }
          throw new Error(`Módulo opcional '${mod}' no está instalado`);
        }
      }
      throw new Error('Importación no permitida: ' + mod);
    };
    return safeRequire;
  }

  private extractVariables(context: vm.Context): Record<string, any> {
    const skip = [
      'console', 'require', 'exports', 'module', 'process',
      'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'
    ];
    const vars: Record<string, any> = {};
    for (const key of Object.keys(context)) {
      if (!skip.includes(key)) {
        vars[key] = (context as any)[key];
      }
    }
    return vars;
  }

  private transpileIfTS(code: string): string {
    if (!this.isTypeScript) return code;
    return ts.transpile(code, { module: ts.ModuleKind.CommonJS, target: ts.ScriptTarget.ES2020 });
  }

  async evaluate(code: string): Promise<EvaluationResult[]> {
    this.logs = [];
    this.coverage.clear();
    this.timeTravel.clear();
    this.performance = [];
    const results: EvaluationResult[] = [];
    const context = this.setupContext();
    const lines = code.split('\n');

    // Evaluar todo el código de una vez para establecer el contexto
    try {
      let fullCode = this.transpileIfTS(code);
      if (/\bawait\b/.test(fullCode)) {
        fullCode = `(async () => {${fullCode}})()`;
        const script = new vm.Script(fullCode);
        await script.runInContext(context, { timeout: 5000 });
      } else {
        const script = new vm.Script(fullCode);
        script.runInContext(context, { timeout: 5000 });
      }
    } catch (err) {
      // Si hay error en la evaluación completa, intentamos evaluación línea por línea
      return this.evaluateLineByLine(code, lines, context);
    }

    // Ahora evaluamos cada línea individualmente para obtener resultados
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line === '' || line.startsWith('//')) {
        continue; // Saltar líneas vacías y comentarios
      }

      const start = Date.now();
      try {
        let result = this.evaluateLineExpression(line, context);
        const duration = Date.now() - start;
        results.push({
          line: i,
          value: result,
          logs: [...this.logs],
          perf: { line: i, durationMs: duration }
        });
        this.coverage.markExecuted(i);
        this.timeTravel.addSnapshot(i, this.extractVariables(context));
        this.performance.push({ line: i, durationMs: duration });
      } catch (err: any) {
        const duration = Date.now() - start;
        // Solo reportar errores significativos, no errores de sintaxis esperados
        if (!this.isExpectedSyntaxError(err.message)) {
          results.push({
            line: i,
            value: undefined,
            error: err.message,
            logs: [...this.logs],
            perf: { line: i, durationMs: duration }
          });
        }
        this.performance.push({ line: i, durationMs: duration });
      }
      this.logs = [];
    }
    return results;
  }

  private async evaluateLineByLine(code: string, lines: string[], context: vm.Context): Promise<EvaluationResult[]> {
    const results: EvaluationResult[] = [];
    let accumulated = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (line.trim() === '') continue;

      accumulated += line + '\n';
      const start = Date.now();

      try {
        let jsCode = this.transpileIfTS(accumulated);
        let result;
        if (/\bawait\b/.test(jsCode)) {
          jsCode = `(async () => {${jsCode}})()`;
          const script = new vm.Script(jsCode);
          result = await script.runInContext(context, { timeout: 5000 });
        } else {
          const script = new vm.Script(jsCode);
          result = script.runInContext(context, { timeout: 5000 });
        }
        const duration = Date.now() - start;
        results.push({ line: i, value: result, logs: [...this.logs], perf: { line: i, durationMs: duration } });
        this.coverage.markExecuted(i);
        this.timeTravel.addSnapshot(i, this.extractVariables(context));
        this.performance.push({ line: i, durationMs: duration });
      } catch (err: any) {
        const duration = Date.now() - start;
        results.push({ line: i, value: undefined, error: err.message, logs: [...this.logs], perf: { line: i, durationMs: duration } });
        this.performance.push({ line: i, durationMs: duration });
      }
      this.logs = [];
    }
    return results;
  }

  private evaluateLineExpression(line: string, context: vm.Context): any {
    // Intentar evaluar la línea como una expresión para obtener su valor
    try {
      // Si es una declaración, intentar obtener el valor de la variable
      if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
        const match = line.match(/(?:let|const|var)\s+(\w+)\s*=\s*(.+);?$/);
        if (match) {
          const varName = match[1];
          return (context as any)[varName];
        }
      }

      // Si es una asignación, devolver el valor asignado
      if (line.includes('=') && !line.includes('==') && !line.includes('!=')) {
        const match = line.match(/(\w+)\s*=\s*(.+);?$/);
        if (match) {
          const varName = match[1];
          return (context as any)[varName];
        }
      }

      // Si es una llamada a función o expresión, evaluarla
      if (!line.includes('function ') && !line.includes('{') && !line.includes('}')) {
        const script = new vm.Script(line.endsWith(';') ? line.slice(0, -1) : line);
        return script.runInContext(context, { timeout: 1000 });
      }

      return undefined;
    } catch (err) {
      throw err;
    }
  }

  private isExpectedSyntaxError(message: string): boolean {
    const expectedErrors = [
      'Unexpected end of input',
      'Unexpected token',
      'Missing } in compound statement',
      'Unexpected identifier'
    ];
    return expectedErrors.some(error => message.includes(error));
  }
}