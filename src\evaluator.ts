import * as vm from 'vm';
import { CoverageTracker } from './coverage';
import { TimeTravelHistory } from './timeTravel';
import * as ts from 'typescript';

export interface EvaluationResult {
  line: number;
  value: any;
  error?: string;
  logs: AdvancedLogEntry[];
  perf?: PerformanceMetric;
}

export interface AdvancedLogEntry {
  timestamp: number;
  category: 'log' | 'error' | 'warn';
  message: string;
}

export interface PerformanceMetric {
  line: number;
  durationMs: number;
}

export class CodeEvaluator {
  private logs: AdvancedLogEntry[] = [];
  public coverage: CoverageTracker;
  public timeTravel: TimeTravelHistory;
  public performance: PerformanceMetric[] = [];
  private isTypeScript: boolean = false;

  constructor() {
    this.coverage = new CoverageTracker();
    this.timeTravel = new TimeTravelHistory();
  }

  setTypeScriptMode(isTS: boolean) {
    this.isTypeScript = isTS;
  }

  private setupContext(): vm.Context {
    const context = vm.createContext({
      console: {
        log: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'log', message: args.join(' ') }),
        error: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'error', message: args.join(' ') }),
        warn: (...args: any[]) => this.logs.push({ timestamp: Date.now(), category: 'warn', message: args.join(' ') }),
      },
      require: this.createSafeRequire(),
      exports: {},
      module: {},
      process: {},
      setTimeout,
      setInterval,
      clearTimeout,
      clearInterval,
    });
    return context;
  }

  private createSafeRequire() {
    // Solo permite importar módulos explícitamente permitidos
    const allowed = ['react', 'react-dom', 'vue', '@vue/runtime-dom', 'fs', 'path'];
    const safeRequire = (mod: string) => {
      if (allowed.includes(mod)) {
        try {
          return require(mod);
        } catch (error) {
          // Si el módulo opcional no está disponible, devolver un mock básico
          if (mod === 'react') {
            return { createElement: () => ({}), useState: () => [null, () => {}], useEffect: () => {} };
          }
          if (mod === 'vue') {
            return { ref: () => ({}), onMounted: () => {} };
          }
          throw new Error(`Módulo opcional '${mod}' no está instalado`);
        }
      }
      throw new Error('Importación no permitida: ' + mod);
    };
    return safeRequire;
  }

  private extractVariables(context: vm.Context): Record<string, any> {
    // Para obtener variables declaradas con let/const/var, necesitamos evaluarlas como scripts
    const vars: Record<string, any> = {};

    // Intentar obtener variables conocidas del contexto
    const skip = [
      'console', 'require', 'exports', 'module', 'process',
      'setTimeout', 'setInterval', 'clearTimeout', 'clearInterval'
    ];

    // Primero obtener propiedades directas del contexto
    for (const key of Object.keys(context)) {
      if (!skip.includes(key)) {
        vars[key] = (context as any)[key];
      }
    }

    return vars;
  }

  private transpileIfTS(code: string): string {
    if (!this.isTypeScript) return code;
    return ts.transpile(code, { module: ts.ModuleKind.CommonJS, target: ts.ScriptTarget.ES2020 });
  }

  async evaluate(code: string): Promise<EvaluationResult[]> {
    this.logs = [];
    this.coverage.clear();
    this.timeTravel.clear();
    this.performance = [];
    const results: EvaluationResult[] = [];
    const context = this.setupContext();
    const lines = code.split('\n');

    // ESTRATEGIA FINAL: Evaluar línea por línea SIN acumular, usando contexto persistente
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Saltar líneas vacías y comentarios
      if (line === '' || line.startsWith('//')) {
        continue;
      }

      const start = Date.now();

      try {
        let result = await this.evaluateLineInContext(line, context);
        const duration = Date.now() - start;

        results.push({
          line: i,
          value: result,
          logs: [...this.logs],
          perf: { line: i, durationMs: duration }
        });

        this.coverage.markExecuted(i);
        this.timeTravel.addSnapshot(i, this.extractVariables(context));
        this.performance.push({ line: i, durationMs: duration });

      } catch (err: any) {
        const duration = Date.now() - start;

        // Solo mostrar errores reales, no errores de sintaxis esperados
        if (!this.isExpectedSyntaxError(err.message)) {
          results.push({
            line: i,
            value: undefined,
            error: err.message,
            logs: [...this.logs],
            perf: { line: i, durationMs: duration }
          });
        }

        this.performance.push({ line: i, durationMs: duration });
      }

      this.logs = [];
    }

    return results;
  }

  private async evaluateLineInContext(line: string, context: vm.Context): Promise<any> {
    // Evaluar una línea individual en el contexto persistente
    try {
      let jsCode = this.transpileIfTS(line);

      // Si es una declaración, ejecutarla y devolver el valor de la variable
      if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
        const script = new vm.Script(jsCode);
        script.runInContext(context, { timeout: 1000 });

        // Extraer el nombre de la variable y devolver su valor
        const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
        if (varMatch) {
          const varName = varMatch[1];
          try {
            // Evaluar la variable como un script separado para obtener su valor
            const getVarScript = new vm.Script(varName);
            return getVarScript.runInContext(context, { timeout: 1000 });
          } catch {
            return undefined;
          }
        }
        return undefined;
      }

      // Si es una asignación, ejecutarla y devolver el valor
      if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
        const script = new vm.Script(jsCode);
        const result = script.runInContext(context, { timeout: 1000 });
        return result;
      }

      // Si es una función, ejecutarla (no devolver nada)
      if (line.includes('function ')) {
        const script = new vm.Script(jsCode);
        script.runInContext(context, { timeout: 1000 });
        return undefined;
      }

      // Si es console.log, ejecutarlo
      if (line.includes('console.log')) {
        const script = new vm.Script(jsCode);
        return script.runInContext(context, { timeout: 1000 });
      }

      // Para cualquier otra expresión, evaluarla
      if (/\bawait\b/.test(jsCode)) {
        jsCode = `(async () => { return ${jsCode.replace(/;$/, '')}; })()`;
        const script = new vm.Script(jsCode);
        return await script.runInContext(context, { timeout: 1000 });
      } else {
        // Si termina en ;, quitarlo para evaluar como expresión
        const cleanLine = jsCode.endsWith(';') ? jsCode.slice(0, -1) : jsCode;
        const script = new vm.Script(cleanLine);
        return script.runInContext(context, { timeout: 1000 });
      }

    } catch (err) {
      throw err;
    }
  }



  private extractLineValue(line: string, context: vm.Context): any {
    try {
      // Si es una declaración de variable, obtener su valor del contexto
      if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
        const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
        if (varMatch) {
          const varName = varMatch[1];
          return (context as any)[varName];
        }
      }

      // Si es una asignación, obtener el valor asignado
      if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
        const assignMatch = /(\w+)\s*=/.exec(line);
        if (assignMatch) {
          const varName = assignMatch[1];
          return (context as any)[varName];
        }
      }

      // Si es una llamada a función, intentar evaluarla
      if (line.includes('(') && line.includes(')') && !line.includes('function')) {
        try {
          const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
          const script = new vm.Script(cleanLine);
          return script.runInContext(context, { timeout: 1000 });
        } catch {
          return undefined;
        }
      }

      // Si es una expresión simple, evaluarla
      if (!line.includes('function') && !line.includes('{') && !line.includes('}') &&
          !line.includes('console.log')) {
        try {
          const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
          const script = new vm.Script(cleanLine);
          return script.runInContext(context, { timeout: 1000 });
        } catch {
          return undefined;
        }
      }

      return undefined;
    } catch {
      return undefined;
    }
  }

  private fallbackEvaluation(code: string, lines: string[], context: vm.Context): EvaluationResult[] {
    const results: EvaluationResult[] = [];

    // Evaluar línea por línea de forma segura
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line === '' || line.startsWith('//')) {
        continue;
      }

      const start = Date.now();

      try {
        // Intentar evaluar solo esta línea
        let result;
        if (line.includes('console.log')) {
          const script = new vm.Script(line);
          result = script.runInContext(context, { timeout: 1000 });
        } else if (!line.includes('let ') && !line.includes('const ') && !line.includes('var ') &&
                   !line.includes('function') && !line.includes('{') && !line.includes('}')) {
          const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
          const script = new vm.Script(cleanLine);
          result = script.runInContext(context, { timeout: 1000 });
        }

        const duration = Date.now() - start;
        results.push({
          line: i,
          value: result,
          logs: [...this.logs],
          perf: { line: i, durationMs: duration }
        });

      } catch (err: any) {
        const duration = Date.now() - start;
        if (!this.isExpectedSyntaxError(err.message)) {
          results.push({
            line: i,
            value: undefined,
            error: err.message,
            logs: [...this.logs],
            perf: { line: i, durationMs: duration }
          });
        }
      }

      this.logs = [];
    }

    return results;
  }

  private getLineResult(line: string, context: vm.Context, fullResult: any): any {
    // Intentar extraer el resultado específico de esta línea
    try {
      // Si es una declaración de variable, obtener su valor
      if (line.startsWith('let ') || line.startsWith('const ') || line.startsWith('var ')) {
        const varMatch = /(?:let|const|var)\s+(\w+)/.exec(line);
        if (varMatch) {
          return (context as any)[varMatch[1]];
        }
      }

      // Si es una asignación, obtener el valor asignado
      if (line.includes('=') && !line.includes('==') && !line.includes('!=') && !line.includes('===')) {
        const assignMatch = /(\w+)\s*=/.exec(line);
        if (assignMatch) {
          return (context as any)[assignMatch[1]];
        }
      }

      // Si es una expresión simple, evaluarla
      if (!line.includes('function') && !line.includes('{') && !line.includes('}')) {
        try {
          const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
          const script = new vm.Script(cleanLine);
          return script.runInContext(context, { timeout: 1000 });
        } catch {
          return fullResult;
        }
      }

      return fullResult;
    } catch {
      return fullResult;
    }
  }

  private evaluateSingleLine(line: string, context: vm.Context): any {
    // Evaluar una línea individual en el contexto actual
    try {
      if (line.includes('console.log')) {
        const script = new vm.Script(line);
        return script.runInContext(context, { timeout: 1000 });
      }

      // Si es una expresión, evaluarla
      if (!line.includes('let ') && !line.includes('const ') && !line.includes('var ') &&
          !line.includes('function') && !line.includes('{') && !line.includes('}')) {
        const cleanLine = line.endsWith(';') ? line.slice(0, -1) : line;
        const script = new vm.Script(cleanLine);
        return script.runInContext(context, { timeout: 1000 });
      }

      return undefined;
    } catch {
      return undefined;
    }
  }

  private isExpectedSyntaxError(message: string): boolean {
    const expectedErrors = [
      'Unexpected end of input',
      'Unexpected token',
      'Missing } in compound statement',
      'Unexpected identifier',
      'already been declared'
    ];
    return expectedErrors.some(error => message.includes(error));
  }
}