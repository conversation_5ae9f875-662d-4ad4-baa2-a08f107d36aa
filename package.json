{"name": "live-code-evaluator", "displayName": "Live Code Evaluator", "description": "Evaluación en tiempo real de código JavaScript/TypeScript con funcionalidades premium tipo Quokka.js.", "version": "1.0.0", "publisher": "live-code-evaluator", "main": "./out/extension.js", "engines": {"vscode": ">=1.60.0"}, "categories": ["Debuggers", "Other"], "activationEvents": ["onStartupFinished", "onLanguage:javascript", "onLanguage:typescript"], "optionalDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "vue": "^3.0.0", "@vue/runtime-dom": "^3.0.0"}, "contributes": {"commands": [{"command": "liveCodeEvaluator.startLiveCoding", "title": "Live Code Evaluator: Start Live Coding", "category": "Live Code Evaluator"}, {"command": "liveCodeEvaluator.stopLiveCoding", "title": "Live Code Evaluator: Stop Live Coding", "category": "Live Code Evaluator"}, {"command": "liveCodeEvaluator.clearResults", "title": "Clear Results"}, {"command": "liveCodeEvaluator.exportResults", "title": "Export Results"}, {"command": "liveCodeEvaluator.toggleCoverage", "title": "Toggle Live Coverage"}, {"command": "liveCodeEvaluator.openTimeTravel", "title": "Open Time Travel Panel"}, {"command": "liveCodeEvaluator.evaluateAllOpenFiles", "title": "Evaluate All Open Files"}, {"command": "liveCodeEvaluator.openGlobalPanel", "title": "Open Global Results Panel"}, {"command": "liveCodeEvaluator.insertSnippet", "title": "Insert Code Snippet"}, {"command": "liveCodeEvaluator.evaluateGitDiff", "title": "Evaluate G<PERSON>"}, {"command": "liveCodeEvaluator.restoreSession", "title": "Restore Last Session"}, {"command": "liveCodeEvaluator.evaluatePython", "title": "Evaluate Python File"}, {"command": "liveCodeEvaluator.explainLineIA", "title": "Explain Line with AI"}, {"command": "liveCodeEvaluator.startLiveShare", "title": "Start Live Share Collaboration"}], "menus": {"commandPalette": [{"command": "liveCodeEvaluator.startLiveCoding", "when": "true"}, {"command": "liveCodeEvaluator.stopLiveCoding", "when": "true"}]}, "configuration": {"title": "Live Code Evaluator", "properties": {"liveCodeEvaluator.autoStart": {"type": "boolean", "default": true, "description": "Auto start live evaluation"}, "liveCodeEvaluator.showInline": {"type": "boolean", "default": true, "description": "Show inline results"}, "liveCodeEvaluator.maxExecutionTime": {"type": "number", "default": 5000, "description": "Max execution time (ms)"}, "liveCodeEvaluator.enableCoverage": {"type": "boolean", "default": true, "description": "Enable live coverage"}, "liveCodeEvaluator.theme": {"type": "string", "default": "default", "enum": ["default", "dark", "light"], "description": "Theme for inline results"}, "liveCodeEvaluator.logLevel": {"type": "string", "default": "info", "description": "Log level"}}}}, "scripts": {"compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/vscode": "^1.60.0"}}