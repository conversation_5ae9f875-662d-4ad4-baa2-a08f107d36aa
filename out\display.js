"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResultDisplayer = void 0;
const vscode = __importStar(require("vscode"));
const THEMES = {
    default: { color: '#888', errorColor: '#ff3333', bgColor: 'rgba(100,255,100,0.08)' },
    dark: { color: '#b3e5fc', errorColor: '#ff8a80', bgColor: 'rgba(33,150,243,0.08)' },
    light: { color: '#607d8b', errorColor: '#e57373', bgColor: 'rgba(255,235,59,0.08)' },
};
class ResultDisplayer {
    constructor() {
        this.currentTheme = 'default';
    }
    ensureTheme(theme) {
        if (theme === this.currentTheme && this.decorationType && this.errorDecorationType && this.coverageDecorationType)
            return;
        const t = THEMES[theme] || THEMES.default;
        this.decorationType = vscode.window.createTextEditorDecorationType({
            after: { color: t.color, margin: '0 0 0 1em' },
        });
        this.errorDecorationType = vscode.window.createTextEditorDecorationType({
            after: { color: t.errorColor, margin: '0 0 0 1em' },
        });
        this.coverageDecorationType = vscode.window.createTextEditorDecorationType({
            isWholeLine: true,
            backgroundColor: t.bgColor,
        });
        this.currentTheme = theme;
    }
    displayResults(editor, results, theme = 'default') {
        this.ensureTheme(theme);
        const decorations = [];
        const errorDecorations = [];
        for (const res of results) {
            const line = res.line;
            const range = new vscode.Range(line, Number.MAX_SAFE_INTEGER, line, Number.MAX_SAFE_INTEGER);
            if (res.error) {
                errorDecorations.push({ range, renderOptions: { after: { contentText: `// Error: ${res.error}` } } });
            }
            else {
                decorations.push({ range, renderOptions: { after: { contentText: `// → ${this.formatDisplayValue(res.value)}` } } });
            }
        }
        if (this.decorationType && this.errorDecorationType) {
            editor.setDecorations(this.decorationType, decorations);
            editor.setDecorations(this.errorDecorationType, errorDecorations);
        }
    }
    displayCoverage(editor, coveredLines) {
        if (!this.coverageDecorationType)
            return;
        const coverageDecorations = [];
        for (const line of coveredLines) {
            const range = new vscode.Range(line, 0, line, 0);
            coverageDecorations.push({ range });
        }
        editor.setDecorations(this.coverageDecorationType, coverageDecorations);
    }
    clearAllDecorations(editor) {
        if (this.decorationType)
            editor.setDecorations(this.decorationType, []);
        if (this.errorDecorationType)
            editor.setDecorations(this.errorDecorationType, []);
        if (this.coverageDecorationType)
            editor.setDecorations(this.coverageDecorationType, []);
    }
    formatDisplayValue(value) {
        if (typeof value === 'object') {
            try {
                return JSON.stringify(value);
            }
            catch {
                return '[Object]';
            }
        }
        return String(value);
    }
}
exports.ResultDisplayer = ResultDisplayer;
//# sourceMappingURL=display.js.map