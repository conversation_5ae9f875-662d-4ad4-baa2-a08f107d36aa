{"version": 3, "file": "autoImportProvider.js", "sourceRoot": "", "sources": ["../src/autoImportProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,MAAM,aAAa,GAA2B;IAC5C,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,OAAO;IACnB,WAAW,EAAE,OAAO;IACpB,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,KAAK;IACZ,WAAW,EAAE,KAAK;IAClB,IAAI,EAAE,IAAI;IACV,MAAM,EAAE,MAAM;IACd,OAAO,EAAE,YAAY,EAAE,gCAAgC;CACxD,CAAC;AAEF,MAAa,kBAAkB;IAK7B,kBAAkB,CAAC,QAA6B,EAAE,KAAsC,EAAE,OAAiC,EAAE,KAA+B;QAC1J,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,UAAU,CAAC,MAAM,KAAK,qBAAqB,EAAE,CAAC;gBAChD,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAClE,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtB,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;wBAC3B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACnF,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,kBAAkB,CAAC,QAA6B,EAAE,OAAe,EAAE,UAAkB;QAC3F,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,WAAW,OAAO,WAAW,UAAU,GAAG,EAAE,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACjH,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,YAAY,OAAO,YAAY,UAAU,MAAM,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;QAC7E,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,OAAO,MAAM,CAAC;IAChB,CAAC;;AA5BH,gDA6BC;AA5BwB,0CAAuB,GAAG;IAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ;CAC/B,CAAC"}